import { useEffect, useState } from 'react'
import { Form, Formik } from 'formik'
import { useSelector } from 'react-redux'
import { Link, useNavigate, useParams } from 'react-router-dom'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import {
  extractDatesFromString,
  getDataFromLocalStorage,
  getPayPeriods,
  hasValues,
  isoToMMDDWithDate,
} from '../../shared/helpers/util'
import ProjectReport from './projectReport/ProjectReport'
import { getMemberPosition } from '../../logic/apis/position'
import { getPaySchedule } from '../../logic/apis/paySchedule'
import Button from '../../shared/components/button/Button'
import { StorageKey, SubscriptionPlanType } from '../../shared/helpers/constants'
import { customCSRReportsPath } from '../../logic/paths'

const reports = {
  weeklySalesReport: 'weekly sales report',
  kpiReport: 'kpi report',
  crewPayrollReport: 'crew payroll report',
  nonCrewPayrollReport: 'non crew payroll report',
  salesPersonReport: 'sales person report',
  customSalesReport: 'custom sales report',
  conversionReport: 'conversion report',
  commissionReport: 'commission report',
  weeklyProductionReport: 'weekly production report',
  productionReport: 'production report',
  weeklyProjectReport: 'weekly project report',
  crewJobCostReport: 'crew job cost report',
  jobCostReport: 'job cost report',
  crewScoreboardReport: 'crew scoreboard report',
}

const Reports = () => {
  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  // const [paySchedule, setPaySchedule] = useState('')
  // const [payScheduleID, setPayScheduleID] = useState('')
  // const [payPeriod, setPayPeriod] = useState('')
  const [weeksOptions, setWeeksOptions] = useState<string[]>([])
  // const [permissionData, setPermissionData] = useState<any[]>([])
  // const [isScheduleData, setIsScheduleData] = useState<any[]>([])
  // const [payPeriodSchedule, setPayPeriodSchedule] = useState<any[]>([])
  // const [getPayPeriodDateData, setGetPayPeriodDateData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const { currentCompany, positionDetails, positionPermissions } = globalSelector.company
  const position = positionDetails?.symbol
  const notVisibleTo = ['SalesPerson', 'RRTech']
  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  const initialValues = {
    dateRange: '',
    data: [],
  }

  useEffect(() => {
    if (hasValues(positionPermissions)) {
      setLoading(false)
    }
  }, [positionPermissions])
  // const handlePermission = async () => {
  //   try {
  //     const permissionResult = await getMemberPosition({ companyId: currentCompany._id })
  //     setPermissionData(permissionResult?.data?.data?.memberPosition?.permissions)
  //   } catch (error) {
  //     console.log('Permission fetch error', error)
  //   } finally {
  //     setLoading(false)
  //   }
  // }

  // useEffect(() => {
  //   if (currentCompany && currentCompany._id) {
  //     handlePermission()
  //   }
  // }, [currentCompany])

  const handleWeeklyReport = async (
    values: any,
    setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void
  ) => {
    try {
      if (values?.dateRange) {
        const [startDate, endDate] = extractDatesFromString(values.dateRange)
        const startYear = startDate.getFullYear()
        const startMonth = String(startDate.getMonth() + 1).padStart(2, '0')
        const startDay = String(startDate.getDate()).padStart(2, '0')
        const endYear = endDate.getFullYear()
        const endMonth = String(endDate.getMonth() + 1).padStart(2, '0')
        const endDay = String(endDate.getDate()).padStart(2, '0')
        // Step 3: Assemble the formatted date string in the desired format
        const formattedStartDate = `${startYear}-${startMonth}-${startDay}`
        const formattedEndDate = `${endYear}-${endMonth}-${endDay}`
        navigate(`/commissionReport/${formattedStartDate}/${formattedEndDate}`)
        // const response = await getWeeklyProductionReport({
        //   companyId: currentCompany._id,
        //   endDate: formattedEndDate,
        //   startDate: formattedStartDate,
        // })
        // setFieldValue('data', response)
      }
    } catch (e) {
      console.log(e)
    }
  }

  useEffect(() => {
    // const now = new Date()
    // const twentyWeeksAgo = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 140)
    // const weeksObj = getWeeksInRange(twentyWeeksAgo, now)
    // const weeksOptionsArr = weeksObj.map((weekData) => `${weekData.startDate} to ${weekData.endDate}`).reverse()
    const data = semiMonthlyPeriods()
    const formatedDate = data.map((data) => `${isoToMMDDWithDate(data.start)} to ${isoToMMDDWithDate(data.end)}`)
    setWeeksOptions(formatedDate)
  }, [])

  const semiMonthlyPeriods = () => {
    //FIRST GET DATES OF THE MONTH
    const today = new Date(new Date().toISOString())
    const currentMonth = today.getMonth()
    const payPeriods = []
    for (let i = 0; i < 6; i++) {
      const start1 = new Date(today)
      start1.setMonth(start1.getMonth() - i)
      start1.setDate(1)
      start1.setHours(0, 0, 0, 0)
      const start2 = new Date(start1)
      start2.setDate(16)
      const end1 = new Date(start2)
      end1.setDate(end1.getDate() - 1)
      end1.setHours(23, 59, 59, 999)
      const end2 = new Date(start1)
      end2.setMonth(end2.getMonth() + 1)
      end2.setDate(end2.getDate() - 1)
      end2.setHours(23, 59, 59, 999)
      const period1 = { start: start1, end: end1 }
      const period2 = { start: start2, end: end2 }
      payPeriods.push(period2)
      payPeriods.push(period1)
    }
    return payPeriods
  }

  // const extractDay = (dateString: any) => {
  //   const date = new Date(dateString)
  //   const day = date.getDate()
  //   return day
  // }
  // function formatEndDate(inputDate: any) {
  //   const date = new Date(inputDate)

  //   const year = date.getFullYear()
  //   const month = ('0' + (date.getMonth() + 1)).slice(-2)
  //   const day = ('0' + date.getDate()).slice(-2)

  //   return `${year}-${month}-${day}`
  // }
  // const formatDate = (date: any) => {
  //   const inputDate = new Date(date)

  //   const month = ('0' + (inputDate.getMonth() + 1)).slice(-2)
  //   const day = ('0' + inputDate.getDate()).slice(-2)
  //   const year = inputDate.getFullYear()

  //   return `${month}-${day}-${year}`
  // }

  // const handleSchedule = async () => {
  //   const result = await getPaySchedule({ companyId: currentCompany._id })
  //   setPayPeriodSchedule(result?.data?.data?.paySchedule)
  //   setIsScheduleData(result?.data?.data?.paySchedule?.map((value: any) => value?.name))
  // }
  // useEffect(() => {
  //   if (currentCompany && currentCompany._id) {
  //     handleSchedule()
  //   }
  // }, [currentCompany])

  // useEffect(() => {
  //   if (paySchedule) {
  //     const filterPayPeriod = payPeriodSchedule?.filter((value: any) => value?.name === paySchedule || [])
  //
  //     setPayScheduleID(filterPayPeriod[0]?._id)
  //     const getPayPeriodData = getPayPeriods(
  //       filterPayPeriod[0]?.period,
  //       formatEndDate(filterPayPeriod[0]?.payPeriodEndsOn),
  //       extractDay(filterPayPeriod[0]?.paydayOn),
  //       formatEndDate(filterPayPeriod[0]?.payPeriodEndsOn2),
  //       extractDay(filterPayPeriod[0]?.paydayOn2)
  //     )
  //     setGetPayPeriodDateData(getPayPeriodData)
  //   }
  // }, [paySchedule])

  return (
    <>
      {loading ? (
        <>
          <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
        </>
      ) : (
        <>
          <SharedStyled.Content
            maxWidth="1270px"
            width="100%"
            disableBoxShadow={true}
            noPadding={true}
            alignItems="flex-start"
            borderRadius="0px"
          >
            <SharedStyled.SectionTitle>Reports</SharedStyled.SectionTitle>
            <SharedStyled.HorizontalDivider />
            <SharedStyled.FlexBox flexDirection="column" alignItems="flex-start" gap="32px" padding="10px 0">
              {/* {permissionData?.filter((value: any) => value?.resource === 'company reports')[0]?.permissions[0] !==
                4 && ( */}

              {positionPermissions[reports.weeklySalesReport] || positionPermissions?.[reports.kpiReport] ? (
                <SharedStyled.FlexCol margin="16px 0 0 0">
                  {isProPlusPlan &&
                    (positionPermissions[reports.weeklySalesReport] || positionPermissions?.[reports.kpiReport]) && (
                      <>
                        <Styled.SubHeading>Company Reports</Styled.SubHeading>
                        <SharedStyled.FlexBox gap="10px" width="100%" marginTop="10px">
                          {positionPermissions[reports.weeklySalesReport] ? (
                            <Button maxWidth="max-content" onClick={() => navigate(`/reports/weekly-report`)}>
                              Weekly Sales Report
                            </Button>
                          ) : null}

                          {/* <SharedStyled.Button maxWidth="max-content">Weekly Report (OLD)</SharedStyled.Button> */}
                          {positionPermissions?.[reports.kpiReport] ? (
                            <Button maxWidth="max-content" onClick={() => navigate(`/kpiReport`)}>
                              KPI Report
                            </Button>
                          ) : null}
                        </SharedStyled.FlexBox>
                      </>
                    )}

                  {/* {permissionData?.filter((value: any) => value?.resource === 'payroll reports')[0]?.permissions[0] !==
                    4 && ( */}
                  <>
                    <SharedStyled.FlexRow margin="10px 0 0 0">
                      {positionPermissions?.[reports.nonCrewPayrollReport] ? (
                        <Link to={`/reports/payroll-report`}>
                          <Button width="max-content">Payroll Report</Button>
                        </Link>
                      ) : null}
                      {positionPermissions?.[reports.crewPayrollReport] ? (
                        <Link to={`/reports/crew-payroll-report`}>
                          <Button width="max-content">Crew Payroll Report</Button>
                        </Link>
                      ) : null}

                      {positionPermissions?.[reports.nonCrewPayrollReport] ? (
                        <Link to={`/reports/noncrew-payroll-report`}>
                          <Button width="max-content">Non Crew Payroll Report</Button>
                        </Link>
                      ) : null}

                      {positionPermissions?.[reports.salesPersonReport] ? (
                        <Button maxWidth="max-content" onClick={() => navigate(`/reports/client-value-report`)}>
                          Client Value Report
                        </Button>
                      ) : null}
                    </SharedStyled.FlexRow>
                  </>

                  {/* // )} */}
                </SharedStyled.FlexCol>
              ) : null}

              {/* )} */}

              {isProPlusPlan && (
                <div>
                  <Styled.SubHeading>Sales Reports</Styled.SubHeading>
                  <SharedStyled.FlexBox gap="10px" width="100%" marginTop="10px">
                    {positionPermissions?.[reports.salesPersonReport] ? (
                      <Button maxWidth="max-content" onClick={() => navigate(`/reports/sale-person-report`)}>
                        Sales Person Report
                      </Button>
                    ) : null}
                    {positionPermissions?.[reports.customSalesReport] ? (
                      <Button maxWidth="max-content" onClick={() => navigate(`/reports/custom-sale-report`)}>
                        Custom Sales Report
                      </Button>
                    ) : null}

                    {/* hardcoded code */}
                    {!notVisibleTo?.includes(position) && positionPermissions?.[reports.conversionReport] ? (
                      <Button maxWidth="max-content" onClick={() => navigate(`/reports/conversion-report`)}>
                        Conversion Report
                      </Button>
                    ) : null}

                    {positionPermissions?.[reports.customSalesReport] ? (
                      <Button maxWidth="max-content" onClick={() => navigate(customCSRReportsPath)}>
                        Custom CSR Report
                      </Button>
                    ) : null}
                  </SharedStyled.FlexBox>
                </div>
              )}

              {isProPlusPlan && positionPermissions?.[reports.commissionReport] ? (
                <div style={{ width: '100%' }}>
                  <Styled.SubHeading>Commission Reports</Styled.SubHeading>
                  <Formik
                    onSubmit={(values, { setFieldValue }) => {
                      handleWeeklyReport(values, setFieldValue)
                    }}
                    initialValues={initialValues}
                  >
                    {({ values, setFieldValue }) => (
                      <Form>
                        <SharedStyled.FlexBox flexDirection="column" gap="0" width="100%">
                          <SharedStyled.FlexBox gap="10px" width="100%" height="60px" heightM="50px">
                            <CustomSelect
                              labelName="Date Range"
                              error={false}
                              value={values.dateRange}
                              dropDownData={weeksOptions}
                              setValue={() => {}}
                              setFieldValue={setFieldValue}
                              margin="10px 0 0 0"
                              stateName="dateRange"
                            />
                            <SharedStyled.FlexRow margin="10px 0 0 0" width="max-contnent">
                              <Button maxWidth="120px" height="100%">
                                Run Report
                              </Button>
                            </SharedStyled.FlexRow>
                          </SharedStyled.FlexBox>
                        </SharedStyled.FlexBox>
                      </Form>
                    )}
                  </Formik>
                </div>
              ) : null}

              {isProPlusPlan && (
                <div>
                  {positionPermissions?.[reports.weeklyProductionReport] ||
                  positionPermissions?.[reports.productionReport] ||
                  positionPermissions?.[reports.weeklyProjectReport] ? (
                    <>
                      <Styled.SubHeading>Operations Reports</Styled.SubHeading>
                      <SharedStyled.FlexBox gap="10px" width="100%" marginTop="16px">
                        {positionPermissions?.[reports.weeklyProductionReport] ? (
                          <Button maxWidth="max-content" onClick={() => navigate(`/reports/weekly-production-report`)}>
                            Weekly Production Report
                          </Button>
                        ) : null}
                        {positionPermissions?.[reports.productionReport] ? (
                          <Button maxWidth="max-content" onClick={() => navigate(`/productionReport`)}>
                            Production Report
                          </Button>
                        ) : null}
                        {positionPermissions?.[reports.weeklyProjectReport] ? (
                          <Button maxWidth="max-content" onClick={() => navigate(`/reports/weekly-project-report`)}>
                            Weekly Project Report
                          </Button>
                        ) : null}
                      </SharedStyled.FlexBox>
                    </>
                  ) : null}
                </div>
              )}

              {positionPermissions?.[reports.crewJobCostReport] ||
              positionPermissions?.[reports.jobCostReport] ||
              positionPermissions?.[reports.crewScoreboardReport] ? (
                <div>
                  <Styled.SubHeading>Project Reports</Styled.SubHeading>
                  <SharedStyled.FlexBox gap="10px" width="100%" marginTop="16px">
                    {positionPermissions?.[reports.crewJobCostReport] ? (
                      <Button maxWidth="max-content" onClick={() => navigate(`/reports/crewProjectReport`)}>
                        Crew Job Cost Report
                      </Button>
                    ) : null}

                    {isProPlusPlan && (
                      <>
                        {positionPermissions?.[reports.jobCostReport] ? (
                          <Button maxWidth="max-content" onClick={() => navigate(`/reports/projectReport`)}>
                            Job Cost Report
                          </Button>
                        ) : null}
                        {positionPermissions?.[reports.crewScoreboardReport] ? (
                          <Button maxWidth="max-content" onClick={() => navigate(`/reports/crew-report`)}>
                            Crew Scoreboard
                          </Button>
                        ) : null}
                      </>
                    )}
                  </SharedStyled.FlexBox>
                </div>
              ) : null}
            </SharedStyled.FlexBox>
          </SharedStyled.Content>
        </>
      )}
    </>
  )
}

export default Reports
