import { lazy } from 'react'
import { Route, Routes, Navigate } from 'react-router-dom'
import * as Paths from '../../../logic/paths'
import { StorageKey, SubscriptionPlanType } from '../../../shared/helpers/constants'

const Signin = lazy(() => import('../../auth/signin/Signin'))
const Signup = lazy(() => import('../../auth/signup/Signup'))
const PieceWorkProSignup = lazy(() => import('../../auth/pieceWorkProSignup/PieceWorkProSignup'))
const Company = lazy(() => import('../../company/Company'))
const Invitation = lazy(() => import('../../invitation/Invitation'))
const PaySchedule = lazy(() => import('../../paySchedules/PaySchedule'))
const Profile = lazy(() => import('../../profile/Profile'))
const CompensationHistory = lazy(() => import('../../team/components/compensationHistory/CompensationHistory'))
import TeamMember from '../../team/components/teamMember/TeamMember'

const AddPaySchedule = lazy(() => import('../../paySchedules/components/addPaySchedule/AddPaySchedule'))
import Team from '../../team/Team'

const EditPaySchedule = lazy(() => import('../../paySchedules/components/editPaySchedule/EditPaySchedule'))
const DeletePaySchedule = lazy(() => import('../../paySchedules/components/deletedPaySchedule/DeletePaySchedule'))
const CrewMember = lazy(() => import('../../crew/components/CrewMember/CrewMember'))
const PieceWorkSettingsSalariedCrew = lazy(
  () => import('../../pieceWorkSettingsSalariedCrew/PieceWorkSettingsSalariedCrew')
)
const ClockInOut = lazy(() => import('../../clockInOut/ClockInOut'))
const ApproveTimeCards = lazy(() => import('../../timeCard/components/approveTimeCards/ApproveTimeCards'))
const DepartmentSettings = lazy(() => import('../../departmentSettings/DepartmentSettings'))

const PositionSettings = lazy(() => import('../../positionSettings/PositionSettings'))

const CompanySettings = lazy(() => import('../../companySettings/CompanySettings'))
const Permission = lazy(() => import('../../positionSettings/components/permission/Permission'))
const CitySettings = lazy(() => import('../../citySettings/CitySettings'))
const DeletedCitySettings = lazy(() => import('../../citySettings/components/deletedCitySettings/DeletedCitySettings'))
const TaskSettings = lazy(() => import('../../taskSettings/TaskSettings'))
const Client = lazy(() => import('../../client/Client'))
const LeadSource = lazy(() => import('../../leadSource/LeadSource'))

const Sales = lazy(() => import('../../sales/Sales'))
const Leads = lazy(() => import('../../leads/Leads'))
const Reports = lazy(() => import('../../reports/Reports'))
const Actions = lazy(() => import('../../actions/Actions'))
const MembersAction = lazy(() => import('../../membersAction/MembersAction'))
const FormBuilder = lazy(() => import('../../formBuilder/FormBuilder'))
const FormBuilderInstance = lazy(() => import('../../formBuilder/FormBuilderInstance'))
const FormFill = lazy(() => import('../../formFill/FormFill'))
const Forms = lazy(() => import('../../forms/Forms'))

import WeeklyReport from '../../reports/weeklyReport/WeeklyReport'
import SalesPersonReport from '../../reports/salesPersonReport/SalesPersonReport'
import WeeklyProductionReport from '../../reports/weeklyProductionReport/WeeklyProductionReport'
import WeeklyProjectReport from '../../reports/weeklyProjectReport/WeeklyProjectReport'

const CustomSalesReport = lazy(() => import('../../reports/customSalesReport/CustomSalesReport'))

import NewLead from '../../newLead/NewLead'
import EditLead from '../../editLead/EditLead'
import CrmSettings from '../../crmSettings/CrmSettings'
import Opportunity from '../../opportunity/Opportunity'
const Units = lazy(() => import('../../units/Units'))
import Materials from '../../materials/Materials'
import Inputs from '../../inputs/Inputs'
// import CrewPositions from '../../crewPositions/CrewPositions'
import ProjectTypes from '../../projectTypes/ProjectTypes'
import Category from '../../category/Category'
import Contract from '../../contract/Contract'
import OrderDetails from '../../orderDetails/OrderDetails'
import OldCompleted from '../../operations/oldCompleted/OldCompleted'
import Refferer from '../../Refferer/Refferer'
const TaxJurisdiction = lazy(() => import('../../taxJurisdiction/TaxJurisdiction'))
const CrewReport = lazy(() => import('../../reports/crewReport/CrewReport'))
const OptionsSettings = lazy(() => import('../../optionsSetting/OptionsSettings'))

const Dashboard = lazy(() => import('../../dashboard/Dashboard'))
import KPIReport from '../../reports/kpiReport/KPIReport'
import ProductionReport from '../../reports/productionReport/ProductionReport'
import CommissionReport from '../../reports/commissionReport/CommissionReport'
import SalesCommission from '../../salesCommission/SalesCommission'
const ProjectReport = lazy(() => import('../../reports/projectReport/ProjectReport'))
const RunPayRoll = lazy(() => import('../../reports/runPayroll/RunPayRoll'))
const Settings = lazy(() => import('../../settings/Settings'))
const NewCrew = lazy(() => import('../../crew/NewCrew'))

const Tasks = lazy(() => import('../../tasks/Tasks'))
import Packages from '../../packages/Packages'
import Operations from '../../operations/Operations'
import Subcontractor from '../../subContractor/Subcontractor'
import AuthLayout from '../layout/AuthLayout'
import DashboardLayout from '../layout/DashboardLayout'
import NotFound from '../notFound/NotFound'
import ClientProfile from '../../client/components/clientProfile/ClientProfile'
import InactiveOpportunity from '../../sales/InactiveOpportunity'
import LostOpportunity from '../../sales/LostOpportunity'
import NewPieceWorkSettingsSalariedCrew from '../../pieceWorkSettingsSalariedCrew/NewPieceWorkSettingsSalariedCrew'
import PieceworkProjects from '../../pieceworkProjects/PieceworkProjects'
import Project from '../../newProject/Project'
const CrewPayrollReport = lazy(() => import('../../reports/runPayroll/CrewPayrollReport'))
import CrewProjectReport from '../../reports/crewProjectReport/CrewProjectReport'
import ConversionReport from '../../reports/conversionReport/ConversionReport'
import Subscribers from '../../subscribers/Subscribers'
import OldCompletedSales from '../../sales/OldCompletedSales'
const Subscription = lazy(() => import('../../subscription/Subscription'))
const Plans = lazy(() => import('../../subscription/Plans'))
import Invoice from '../../subscription/Invoice'
const Help = lazy(() => import('../../help/Help'))
import DeletedTimecards from '../../timeCard/components/deletedTimeCards/DeletedTimecards'
import LostLeads from '../../leads/LostLeads'
import InactiveLeads from '../../leads/InactiveLeads'
import ContractsSetting from '../../contractsSetting/ContractsSetting'
import LeadsPositionSettings from '../../settings/leadsPositions/LeadsPositionSettings'
import SignUpSucess from '../../auth/pieceWorkProSignup/components/SignUpSucess'
import MediaSettings from '../../mediaSettings/MediaSettings'
import Media from '../../media/Media'
import DeletedSales from '../../sales/DeletedSales'
import ClientValueReport from '../../reports/clientValueReport/ClientValueReport'
import Advertise from '../../leadSource/Advertise'
import PayrollReport from '../../reports/runPayroll/PayrollReport'
import ProjectTypeDetails from '../../projectTypes/components/projectTypeModal/ProjectTypeDetails'
import Contact from '../../contact/Contact'
import ContactProfile from '../../contact/components/contactProfile/ContactProfile'
import CustomCSRReport from '../../reports/customCSRReport/CustomCSRReport'
const DeletedLeads = lazy(() => import('../../leads/DeletedLeads'))

const AllMedia = lazy(() => import('../../media/AllMedia'))
const ShareMedia = lazy(() => import('../../media/components/ShareMedia'))
const GpsMedia = lazy(() => import('../../media/components/GpsMedia'))
const AdminSetting = lazy(() => import('../../adminSettings/AdminSetting'))
const NoncrewPayroll = lazy(() => import('../../reports/runPayroll/NoncrewPayroll'))

const Track = lazy(() => import('../../track/Track'))
const ResetPassword = lazy(() => import('../../auth/resetPassword/ResetPassword'))
const ForgotPassword = lazy(() => import('../../auth/forgotPassword/ForgotPassword'))

export interface RouteDefinition {
  path: string
  protected?: boolean
  redirect?: string
  element?: any
  routes?: RouteDefinition[]
  title?: string
  requires?: any
  pathType?: number
}
const notFoundRoute: RouteDefinition[] = [
  {
    path: '*',
    element: NotFound,
    protected: false,
    title: 'Error page',
  },
]

const authRoutes = [
  {
    path: Paths.rootPath,
    element: Signin,
    protected: false,
    title: 'Home',
    pathType: 0,
  },
  {
    path: Paths.companySignupPath,
    element: Signup,
    protected: false,
    title: 'Signup',
    pathType: 0,
  },
  {
    path: Paths.signupPath,
    element: PieceWorkProSignup,
    protected: false,
    title: 'PieceWorkProSignup',
    pathType: 0,
  },
  {
    path: Paths.customSignUpPath,
    element: PieceWorkProSignup,
    protected: false,
    title: 'PieceWorkProSignup',
    pathType: 0,
  },
  {
    path: Paths.signupSuccess,
    element: SignUpSucess,
    protected: false,
    title: 'PieceWorkProSignup',
    pathType: 0,
  },
  {
    path: Paths.signinPath,
    element: Signin,
    protected: false,
    title: 'Signin',
    pathType: 0,
  },
  {
    path: Paths.forgotPasswordPath,
    element: ForgotPassword,
    protected: false,
    title: 'ForgotPassword',
    pathType: 0,
  },
  {
    path: Paths.resetPasswordPath,
    element: ResetPassword,
    protected: false,
    title: 'ResetPassword',
    pathType: 0,
  },
  {
    path: Paths.invitationPath,
    element: Invitation,
    protected: false,
    title: 'Invitation',
    pathType: 0,
  },

  {
    path: Paths.plansPath,
    element: Plans,
    protected: false,
    title: 'Plans',
    pathType: 0,
  },
  {
    path: Paths.shareMediaPath,
    element: ShareMedia,
    protected: false,
    title: 'Plans',
    pathType: 0,
  },
]

function getRouteRenderWithAuth(isLoggedIn: boolean, route: RouteDefinition) {
  if (isLoggedIn === route.protected || !route.redirect) {
    const RouteComponent = route.requires ? route.requires(route.element) : route.element
    return { element: <RouteComponent /> }
  } else {
    // return (routeProps: any) => {
    // const from = route.redirect == '/login' ? `?from=${routeProps.match.url}` : ''
    return { element: <Navigate replace to={route.redirect} /> }
    // }
  }
}
const NewRoute = () => {
  const isLoggedIn = false

  const planType = JSON.parse(localStorage.getItem(StorageKey.plan)!) || ''

  const isProPlusPlan = planType === SubscriptionPlanType.PROPLUS

  const protectedRoutes: RouteDefinition[] = [
    {
      path: Paths.subcontractorPath,
      element: Subcontractor,
      title: 'Signin',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.helpPath,
      element: Help,
      title: 'help',
      pathType: 0,
    },
    {
      path: Paths.deletedTimeCardPath,
      element: DeletedTimecards,
      title: 'timecard',
      pathType: 0,
    },

    {
      path: Paths.crewPath,
      element: NewCrew,
      protected: false,
      title: 'Signin',
      pathType: 0,
    },

    {
      path: Paths.profilePath,
      element: Profile,
      protected: false,
      title: 'Profile',
      pathType: 0,
    },
    // {
    //   path: Paths.companyPath,
    //   element: Company,
    //   protected: false,
    //   title: 'Company',
    //   pathType: 0,
    // },
    {
      path: Paths.teamPath,
      element: Team,
      protected: false,
      title: 'Team',
      pathType: 0,
    },

    {
      path: Paths.teamMemberPath,
      element: TeamMember,
      protected: false,
      title: 'TeamMember',
      pathType: 0,
    },
    {
      path: Paths.invoicePath,
      element: Invoice,
      protected: false,
      title: 'TeamMember',
      pathType: 0,
    },
    {
      path: Paths.compensationHistoryPath,
      element: CompensationHistory,
      protected: false,
      title: 'CompensationHistory',
      pathType: 0,
    },

    {
      path: Paths.subscribersPath,
      element: Subscribers,
      protected: false,
      title: 'Subscribers',
      pathType: 0,
    },
    {
      path: Paths.subscriptionPath,
      element: Subscription,
      protected: false,
      title: 'Subscribers',
      pathType: 0,
    },
    {
      path: Paths.settingsPath,
      element: Settings,
      protected: false,
      title: 'Settings',
      pathType: 0,
    },
    {
      path: Paths.paySchedulePath,
      element: PaySchedule,
      protected: false,
      title: 'PaySchedule',
      pathType: 0,
    },
    {
      path: Paths.addPaySchedulePath,
      element: AddPaySchedule,
      protected: false,
      title: 'AddPaySchedule',
      pathType: 0,
    },
    {
      path: Paths.editPaySchedulePath,
      element: EditPaySchedule,
      protected: false,
      title: 'EditPaySchedule',
      pathType: 0,
    },
    {
      path: Paths.deletePaySchedulePath,
      element: DeletePaySchedule,
      protected: false,
      title: 'DeletePaySchedule',
      pathType: 0,
    },

    {
      path: Paths.pieceWorkSettingsSalariedCrewPath,
      element: NewPieceWorkSettingsSalariedCrew, // New dashoard
      protected: false,
      title: 'PieceWorkSettingsSalariedCrew',
      pathType: 0,
    },

    {
      path: Paths.clockInOutPath,
      element: ClockInOut,
      protected: false,
      title: 'ClockInOut',
      pathType: 0,
    },

    {
      path: Paths.approveTimeCardPath,
      element: ApproveTimeCards,
      protected: false,
      title: 'ApproveTimeCards',
      pathType: 0,
    },
    {
      path: Paths.departmentSettingsPath,
      element: DepartmentSettings,
      protected: false,
      title: 'DepartmentSettings',
      pathType: 0,
    },
    {
      path: Paths.adminSettingsPath,
      element: AdminSetting,
      protected: false,
      title: 'AdminSettings',
      pathType: 0,
    },

    {
      path: Paths.positionSettingsPath,
      element: PositionSettings,
      protected: false,
      title: 'PositionSettings',
      pathType: 0,
    },
    // {
    //   path: Paths.leadsPositionSettingsPath,
    //   element: LeadsPositionSettings,
    //   protected: false,
    //   title: 'PositionSettings',
    //   pathType: 0,
    // },
    {
      path: Paths.companySettingsPath,
      element: CompanySettings,
      protected: false,
      title: 'CompanySettings',
      pathType: 0,
    },
    {
      path: Paths.permissionPositionSettingsPath,
      element: Permission,
      protected: false,
      title: 'Permission',
      pathType: 0,
    },

    {
      path: Paths.citySettingsPath,
      element: CitySettings,
      protected: false,
      title: 'CitySettings',
      pathType: 0,
    },
    {
      path: Paths.deletedCitySettingsPath,
      element: DeletedCitySettings,
      protected: false,
      title: 'DeletedCitySettings',
      pathType: 0,
    },

    {
      path: Paths.taskSettingsPath,
      element: TaskSettings,
      protected: false,
      title: 'TaskSettings',
      pathType: 0,
    },

    {
      path: Paths.contactPath,
      element: Contact,
      protected: true,
      title: 'Contact',
      redirect: Paths.profilePath,
      pathType: 0,
    },

    {
      path: Paths.contactProfilePath,
      element: ContactProfile,
      title: 'ContactProfile',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },

    {
      path: Paths.clientPath,
      element: Client,
      protected: true,
      title: 'Client',
      redirect: Paths.profilePath,
      pathType: 0,
    },

    {
      path: Paths.clientProfilePath,
      element: ClientProfile,
      title: 'ClientProfile',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.leadSourceSettingsPath,
      element: LeadSource,
      protected: true,
      redirect: Paths.profilePath,
      title: 'LeadSource',
      pathType: 0,
    },
    {
      path: Paths.reffererSettingsPath,
      element: Refferer,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Referrer',
      pathType: 0,
    },

    {
      path: Paths.advertiseSettingsPath,
      element: Advertise,
      protected: true,
      redirect: Paths.profilePath,
      title: 'AdvertiseSettings',
      pathType: 0,
    },

    {
      path: Paths.optionsSettingsPath,
      element: OptionsSettings,
      protected: true,
      redirect: Paths.profilePath,
      title: 'OptionsSettings',
      pathType: 0,
    },
    {
      path: Paths.leadsPath,
      element: Leads,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Leads',
      pathType: 0,
    },
    {
      path: Paths.deletedLeads,
      element: DeletedLeads,
      protected: true,
      redirect: Paths.profilePath,
      title: 'DeletedLeads',
      pathType: 0,
    },
    {
      path: Paths.salesPath,
      element: Sales,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Sales',
      pathType: 0,
    },
    {
      path: Paths.deletedSales,
      element: DeletedSales,
      protected: true,
      redirect: Paths.profilePath,
      title: 'DeletedSales',
      pathType: 0,
    },
    {
      path: Paths.oldCompletedSales,
      element: OldCompletedSales,
      title: 'Old-Sales',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.newLeadPath,
      element: NewLead,
      protected: false,
      title: 'New Lead',
      pathType: 0,
    },
    {
      path: Paths.editLeadPath,
      element: EditLead,
      protected: false,
      title: 'New Lead',
      pathType: 0,
    },
    {
      path: Paths.lostLeads,
      element: LostLeads,
      protected: false,
      title: 'Lost Lead',
      pathType: 0,
    },
    {
      path: Paths.inActiveLeads,
      element: InactiveLeads,
      protected: false,
      title: 'Inactive Lead',
      pathType: 0,
    },
    {
      path: Paths.crmSettingsPath,
      element: CrmSettings,
      protected: true,
      redirect: Paths.profilePath,
      title: 'CRM Settings',
      pathType: 0,
    },
    {
      path: Paths.contractsSettingsPath,
      element: ContractsSetting,
      protected: true,
      title: 'Contracts Settings',
      pathType: 0,
    },
    {
      path: Paths.reportsPath,
      element: Reports,
      protected: false,
      title: 'Reports',
      pathType: 0,
    },
    {
      path: Paths.actionsPath,
      element: Actions,
      protected: false,
      title: 'Actions',
      pathType: 0,
    },
    {
      path: Paths.formsBuilder,
      element: FormBuilder,
      protected: false,
      title: 'FormBuilder',
      pathType: 0,
    },
    {
      path: Paths.formsBuilderCreation,
      element: FormBuilderInstance,
      protected: false,
      title: 'FormBuilderInstance',
      pathType: 0,
    },
    {
      path: Paths.oppFormFill,
      element: FormFill,
      protected: false,
      title: 'FormFill',
      pathType: 0,
    },
    {
      path: Paths.FormFill,
      element: FormFill,
      protected: false,
      title: 'FormFill',
      pathType: 0,
    },
    {
      path: Paths.otherFormFill,
      element: FormFill,
      protected: false,
      title: 'FormFill',
      pathType: 0,
    },
    {
      path: Paths.formsPath,
      element: Forms,
      protected: false,
      title: 'Forms',
      pathType: 0,
    },
    {
      path: Paths.membersAction,
      element: MembersAction,
      protected: false,
      title: 'MembersAction',
      pathType: 0,
    },
    {
      path: Paths.weeklyReportsPath,
      element: WeeklyReport,
      title: 'Weekly Report',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.salesPersonReportsPath,
      element: SalesPersonReport,
      title: 'Weekly Report',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.conversionReportPath,
      element: ConversionReport,
      title: 'Conversion Report',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.customSalesReportsPath,
      element: CustomSalesReport,
      title: 'Weekly Report',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.customCSRReportsPath,
      element: CustomCSRReport,
      title: 'Weekly Report',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.opportunityPath,
      element: Opportunity,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Opportunity',
      pathType: 0,
    },
    {
      path: Paths.newProjectPath,
      element: Project,
      protected: true,
      redirect: Paths.profilePath,
      title: 'New Project',
      pathType: 0,
    },
    {
      path: Paths.newProjectPathOperations,
      element: Project,
      protected: true,
      redirect: Paths.profilePath,
      title: 'New Project for Operations',
      pathType: 0,
    },
    {
      path: Paths.editProjectPath,
      element: Project,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Edit Project',
      pathType: 0,
    },
    {
      path: Paths.editProjectPathOperations,
      element: Project,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Edit Project for Operations',
      pathType: 0,
    },
    {
      path: Paths.clientInfoPath,
      element: Contract,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Contract',
      pathType: 0,
    },
    {
      path: Paths.trackPath,
      element: Track,
      protected: false,
      title: 'Contract',
      pathType: 0,
    },
    {
      path: Paths.mediaGpsPath,
      element: GpsMedia,
      protected: false,
      title: 'Gps',
      pathType: 0,
    },
    {
      path: Paths.allMediaPath,
      element: AllMedia,
      protected: false,
      title: 'Gps',
      pathType: 0,
    },
    {
      path: Paths.clientInfoPathOperations,
      element: Contract,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Contract for Operations',
      pathType: 0,
    },
    {
      path: Paths.orderDetails,
      element: OrderDetails,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Order Details',
      pathType: 0,
    },
    {
      path: Paths.orderDetailsOperations,
      element: OrderDetails,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Order Details for Operations',
      pathType: 0,
    },

    {
      path: Paths.unitsSettingsPath,
      element: Units,
      protected: false,
      title: 'Units Settings',
      pathType: 0,
    },
    {
      path: Paths.mediaSettingsPath,
      element: MediaSettings,
      protected: false,
      title: 'Media Settings',
      pathType: 0,
    },
    {
      path: Paths.salesMediaPath,
      element: Media,
      protected: false,
      title: 'Media sales',
      pathType: 0,
    },
    {
      path: Paths.opportunityMediaPath,
      element: Media,
      protected: false,
      title: 'Media opps',
      pathType: 0,
    },
    {
      path: Paths.materialsSettingsPath,
      element: Materials,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Materials Settings',
      pathType: 0,
    },
    {
      path: Paths.inputsSettingsPath,
      element: Inputs,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Inputs Settings',
      pathType: 0,
    },
    // {
    //   path: Paths.crewPosSettingsPath,
    //   element: CrewPositions,
    //   protected: true,
    //   redirect: Paths.profilePath,
    //   title: 'Crew Positions Settings',
    //   pathType: 0,
    // },
    {
      path: Paths.projectTypeSettingsPath,
      element: ProjectTypes,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Project Types Settings',
      pathType: 0,
    },
    {
      path: Paths.projectTypeDetailsPath,
      element: ProjectTypeDetails,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Project Types Details',
      pathType: 0,
    },
    {
      path: Paths.categorySettingsPath,
      element: Category,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Category Settings',
      pathType: 0,
    },

    {
      path: Paths.tasksPath,
      element: Tasks,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Tasks',
      pathType: 0,
    },
    {
      path: Paths.packagesPath,
      element: Packages,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Packages',
      pathType: 0,
    },

    {
      path: Paths.operationsPath,
      element: Operations,
      title: 'Operations',
      protected: true,
      redirect: Paths.profilePath,
      pathType: 0,
    },
    {
      path: Paths.opportunityOperations,
      element: Opportunity,
      protected: true,
      redirect: Paths.profilePath,
      title: 'Operations',
      pathType: 0,
    },
    {
      path: Paths.oldCompleted,
      element: OldCompleted,
      protected: true,
      redirect: Paths.profilePath,
      title: 'OldCompleted',
      pathType: 0,
    },
    {
      path: Paths.inActiveOperations,
      element: InactiveOpportunity,
      title: 'InactiveOpportunity',
      protected: true,
      redirect: Paths.profilePath,
      pathType: 0,
    },
    {
      path: Paths.lostOperations,
      element: LostOpportunity,
      title: 'LostOpportunity',
      protected: true,
      redirect: Paths.profilePath,
      pathType: 0,
    },
    {
      path: Paths.taxJurisdiction,
      element: TaxJurisdiction,
      protected: true,
      redirect: Paths.profilePath,
      title: 'TaxJurisdiction',
      pathType: 0,
    },
    {
      path: Paths.salesCommission,
      element: SalesCommission,
      protected: true,
      redirect: Paths.profilePath,
      title: 'salesCommission',
      pathType: 0,
    },
    {
      path: Paths.weeklyProductionReport,
      element: WeeklyProductionReport,
      title: 'WeeklyProductionReport',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.weeklyProjectReport,
      element: WeeklyProjectReport,
      title: 'WeeklyProjectReport',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.crewReport,
      element: CrewReport,
      title: 'CrewReport',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.dashboardPath,
      element: Dashboard,
      protected: false,
      title: 'Dashboard',
      pathType: 0,
    },
    {
      path: Paths.kpiReportPath,
      element: KPIReport,
      title: 'KPIReport',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.productionReportPath,
      element: ProductionReport,
      title: 'ProductionReport',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.commissionReportPath,
      element: CommissionReport,
      title: 'Commission Report',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.crewProjectReportPath,
      element: CrewProjectReport,
      protected: false,
      title: 'Crew Project Report',
      pathType: 0,
    },
    {
      path: Paths.projectReportPath,
      element: ProjectReport,
      title: 'Project Report',
      pathType: 0,
      protected: true,
      redirect: Paths.profilePath,
    },
    {
      path: Paths.projectsPath,
      element: PieceworkProjects,
      protected: false,
      title: 'Piece-work',
      pathType: 0,
    },
    {
      path: Paths.PayRollPath,
      element: PayrollReport,
      protected: false,
      title: 'Run PayRoll',
      pathType: 0,
    },
    {
      path: Paths.crewPayRollPath,
      element: CrewPayrollReport,
      protected: false,
      title: 'Run PayRoll',
      pathType: 0,
    },
    {
      path: Paths.nonCrewPayRollPath,
      element: NoncrewPayroll,
      protected: false,
      title: 'Run PayRoll',
      pathType: 0,
    },
    {
      path: Paths.clientValueReport,
      element: ClientValueReport,
      protected: false,
      title: 'Client Value Report',
      pathType: 0,
    },
    {
      path: Paths.plansPath,
      element: Plans,
      protected: false,
      title: 'Plans',
      pathType: 0,
    },
  ]

  const mapRoutes = (route: RouteDefinition, i: number) => {
    const render = getRouteRenderWithAuth(isProPlusPlan, route)

    return <Route key={i} path={route.path} {...render} />
  }
  return (
    <Routes>
      <Route element={<AuthLayout />}>{authRoutes?.map(mapRoutes)}</Route>

      <Route element={<DashboardLayout />}>{protectedRoutes?.map(mapRoutes)}</Route>
      <Route>{notFoundRoute?.map(mapRoutes)}</Route>
    </Routes>
  )
}

export default NewRoute
