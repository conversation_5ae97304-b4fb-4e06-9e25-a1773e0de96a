# New Heights Roofing - Comprehensive Code Index

## Overview
This document provides a detailed index of the New Heights Roofing React application codebase, including file locations, component purposes, and architectural patterns.

## Entry Points

### Main Application Entry
- **`src/index.tsx`**: Application entry point with React DOM rendering, Redux Provider, and Router setup
- **`src/modules/app/App.tsx`**: Main App component with theme provider, global styles, and route configuration
- **`src/modules/app/routes/NewRoute.tsx`**: Central routing configuration with lazy-loaded components

## Core Architecture Files

### Configuration
- **`package.json`**: Dependencies, scripts, and project metadata
- **`tsconfig.json`**: TypeScript configuration
- **`webpack/`**: Custom Webpack configuration for development and production
  - `webpack.common.js`: Shared webpack configuration
  - `webpack.dev.js`: Development-specific configuration
  - `webpack.prod.js`: Production-specific configuration
- **`commitlint.config.js`**: Commit message linting configuration

### Styling & Theming
- **`src/styles/globalStyle.ts`**: Global CSS styles using styled-components
- **`src/styles/theme.ts`**: Theme configuration and color schemes
- **`src/styles/styled.ts`**: Styled-components utilities
- **`src/index.css`**: Base CSS styles

## State Management (Redux)

### Store Configuration
- **`src/logic/redux/store.tsx`**: Redux store setup with DevTools integration
- **`src/logic/redux/reduxHook.ts`**: Typed useSelector and useDispatch hooks

### Actions
- **`src/logic/redux/actions/auth.ts`**: Authentication actions
- **`src/logic/redux/actions/company.ts`**: Company management actions
- **`src/logic/redux/actions/invitation.ts`**: User invitation actions
- **`src/logic/redux/actions/timeZone.ts`**: Timezone configuration actions
- **`src/logic/redux/actions/ui.ts`**: UI state actions

### Reducers
- **`src/logic/redux/reducers/auth.ts`**: Authentication state reducer
- **`src/logic/redux/reducers/company.ts`**: Company state reducer
- **`src/logic/redux/reducers/invitation.ts`**: Invitation state reducer
- **`src/logic/redux/reducers/timeZone.ts`**: Timezone state reducer
- **`src/logic/redux/reducers/ui.ts`**: UI state reducer
- **`src/logic/redux/reducers/index.ts`**: Root reducer combining all reducers

## API Layer

### Core Services
- **`src/logic/apis/auth.ts`**: Authentication and authorization API calls
- **`src/logic/apis/company.ts`**: Company management API
- **`src/logic/apis/team.ts`**: Team and member management API
- **`src/logic/apis/profile.ts`**: User profile operations
- **`src/logic/apis/invitation.ts`**: User invitation system API

### Business Domain APIs
- **`src/logic/apis/client.ts`**: Client management operations
- **`src/logic/apis/contact.ts`**: Contact management API
- **`src/logic/apis/sales.ts`**: Sales pipeline operations
- **`src/logic/apis/projects.ts`**: Project management API
- **`src/logic/apis/crew.ts`**: Crew management operations
- **`src/logic/apis/subcontractor.ts`**: Subcontractor operations

### Configuration APIs
- **`src/logic/apis/city.ts`**: City/location management
- **`src/logic/apis/department.ts`**: Department settings API
- **`src/logic/apis/position.ts`**: Position and role management
- **`src/logic/apis/leadSource.ts`**: Lead source configuration
- **`src/logic/apis/marketingChannel.ts`**: Marketing channel management

### Operational APIs
- **`src/logic/apis/clockInOut.ts`**: Time tracking operations
- **`src/logic/apis/approveTimeCard.ts`**: Timecard approval API
- **`src/logic/apis/paySchedule.ts`**: Pay schedule management
- **`src/logic/apis/pieceWork.ts`**: Piece work management
- **`src/logic/apis/pieceWorkPro.ts`**: Advanced piece work operations

### Reporting & Analytics
- **`src/logic/apis/report.ts`**: General reporting API
- **`src/logic/apis/dashboard.ts`**: Dashboard data API
- **`src/logic/apis/compensation.ts`**: Compensation calculations

### Utility APIs
- **`src/logic/apis/form.ts`**: Form management API
- **`src/logic/apis/media.ts`**: Media upload and management
- **`src/logic/apis/gps.ts`**: GPS tracking API
- **`src/logic/apis/task.ts`**: Task management API
- **`src/logic/apis/useFetch.ts`**: Custom fetch hook
- **`src/logic/apis/wheatherApi.ts`**: Weather data integration
- **`src/logic/apis/index.ts`**: API exports and utilities

## Routing System

### Route Definitions
- **`src/logic/paths.ts`**: Centralized route path constants with TypeScript interfaces
- **`src/modules/app/routes/NewRoute.tsx`**: Main routing component with lazy loading

### Layouts
- **`src/modules/app/layout/AuthLayout.tsx`**: Layout for authentication pages
- **`src/modules/app/layout/DashboardLayout.tsx`**: Layout for authenticated dashboard pages

### Navigation
- **`src/modules/app/navbar/`**: Top navigation components
- **`src/modules/app/sidebar/`**: Side navigation components
- **`src/modules/app/Newsidebar/`**: Updated sidebar implementation

## Module Structure

### Authentication Module (`src/modules/auth/`)
- **`signin/Signin.tsx`**: User login component
- **`signup/Signup.tsx`**: Company registration component
- **`pieceWorkProSignup/PieceWorkProSignup.tsx`**: Specialized signup for contractors
- **`forgotPassword/ForgotPassword.tsx`**: Password recovery component
- **`resetPassword/ResetPassword.tsx`**: Password reset component

### Dashboard Module (`src/modules/dashboard/`)
- **`Dashboard.tsx`**: Main dashboard with KPIs and analytics
- **`components/`**: Dashboard-specific components
- **`style.ts`**: Dashboard styling

### Team Management (`src/modules/team/`)
- **`Team.tsx`**: Main team overview page
- **`components/teamMember/TeamMember.tsx`**: Individual team member management
- **`components/compensationHistory/CompensationHistory.tsx`**: Compensation tracking
- **`style.ts`**: Team module styling

### Sales Management (`src/modules/sales/`)
- **`Sales.tsx`**: Main sales pipeline interface
- **`AddOpportunityModal.tsx`**: Modal for creating new opportunities
- **`DeletedSales.tsx`**: Management of deleted sales records
- **`InactiveOpportunity.tsx`**: Handling inactive opportunities
- **`LostOpportunity.tsx`**: Tracking lost opportunities
- **`OldCompletedSales.tsx`**: Historical sales data view
- **`style.ts`**: Sales module styling

### Operations Management (`src/modules/operations/`)
- **`Operations.tsx`**: Main operations dashboard
- **`component/`**: Operations-specific components
- **`oldCompleted/OldCompleted.tsx`**: Historical operations data
- **`style.ts`**: Operations module styling

### Leads Management (`src/modules/leads/`)
- **`Leads.tsx`**: Main lead management interface
- **`AddNewLead.tsx`**: Component for creating new leads
- **`DeletedLeads.tsx`**: Management of deleted leads
- **`InactiveLeads.tsx`**: Handling inactive leads
- **`LostLeads.tsx`**: Tracking lost leads
- **`style.ts`**: Leads module styling

### Project Management
- **`src/modules/newProject/`**: Project creation and management
  - `NewProject.tsx`: New project creation
  - `Project.tsx`: Main project component
  - `components/`: Project-related components
  - `style.ts`: Project styling
  - `types.ts`: Project type definitions

- **`src/modules/opportunity/`**: Opportunity management
  - `Opportunity.tsx`: Main opportunity component
  - `components/`: Opportunity-related components
  - `styles.ts`: Opportunity styling

- **`src/modules/contract/`**: Contract management
  - `Contract.tsx`: Main contract component
  - `components/`: Contract-related components
  - `constant.ts`: Contract constants
  - `signContract.ts`: Contract signing logic
  - `style.ts`: Contract styling

- **`src/modules/orderDetails/`**: Order detail management
  - `OrderDetails.tsx`: Main order details component
  - `OrderDetailsOld.tsx`: Legacy order details
  - `components/`: Order detail components
  - `constant.ts`: Order constants
  - `styles.ts`: Order styling

### Reporting Module (`src/modules/reports/`)
- **`Reports.tsx`**: Main reporting dashboard
- **`weeklyReport/WeeklyReport.tsx`**: Weekly business reports
- **`salesPersonReport/SalesPersonReport.tsx`**: Sales performance reports
- **`weeklyProductionReport/WeeklyProductionReport.tsx`**: Production metrics
- **`crewReport/CrewReport.tsx`**: Crew performance reports
- **`kpiReport/KPIReport.tsx`**: Key performance indicators
- **`productionReport/ProductionReport.tsx`**: Production analytics
- **`commissionReport/CommissionReport.tsx`**: Commission calculations
- **`runPayroll/RunPayRoll.tsx`**: Payroll processing
- **`clientValueReport/ClientValueReport.tsx`**: Client value analysis
- **`marketingReport/MarketingReport.tsx`**: Marketing effectiveness
- **`style.ts`**: Reports module styling

### Settings Module (`src/modules/settings/`)
- **`Settings.tsx`**: Main settings interface
- **`components/`**: Settings-related components
- **`leadsPositions/LeadsPositionSettings.tsx`**: Lead position configuration
- **`style.ts`**: Settings module styling

### Administrative Settings
- **`src/modules/adminSettings/AdminSetting.tsx`**: Administrative configuration
- **`src/modules/companySettings/CompanySettings.tsx`**: Company-wide settings
- **`src/modules/departmentSettings/DepartmentSettings.tsx`**: Department management
- **`src/modules/positionSettings/PositionSettings.tsx`**: Position and role management
- **`src/modules/citySettings/CitySettings.tsx`**: City/location settings
- **`src/modules/taskSettings/TaskSettings.tsx`**: Task configuration
- **`src/modules/crmSettings/CrmSettings.tsx`**: CRM configuration
- **`src/modules/contractsSettings/ContractsSetting.tsx`**: Contract templates
- **`src/modules/mediaSettings/MediaSettings.tsx`**: Media management settings

### Business Configuration Modules
- **`src/modules/client/Client.tsx`**: Client management interface
- **`src/modules/contact/Contact.tsx`**: Contact management interface
- **`src/modules/leadSource/LeadSource.tsx`**: Lead source tracking
- **`src/modules/marketingChannel/`**: Marketing channel management
- **`src/modules/materials/Materials.tsx`**: Materials catalog management
- **`src/modules/units/Units.tsx`**: Unit management interface
- **`src/modules/inputs/Inputs.tsx`**: Input management
- **`src/modules/category/Category.tsx`**: Category management
- **`src/modules/projectTypes/ProjectTypes.tsx`**: Project type definitions
- **`src/modules/taxJurisdiction/TaxJurisdiction.tsx`**: Tax jurisdiction settings
- **`src/modules/packages/Packages.tsx`**: Package management
- **`src/modules/tasks/Tasks.tsx`**: Task management interface

### Time & Payroll Modules
- **`src/modules/clockInOut/ClockInOut.tsx`**: Time tracking interface
- **`src/modules/timeCard/`**: Timecard management components
- **`src/modules/paySchedules/PaySchedule.tsx`**: Pay schedule configuration
- **`src/modules/pieceWorkSettingsSalariedCrew/`**: Piece work settings
- **`src/modules/pieceworkProjects/PieceworkProjects.tsx`**: Piece work project management
- **`src/modules/salesCommission/SalesCommission.tsx`**: Sales commission management

### Utility & Support Modules
- **`src/modules/help/Help.tsx`**: Help and support interface
- **`src/modules/media/Media.tsx`**: Media management interface
- **`src/modules/track/Track.tsx`**: GPS tracking interface
- **`src/modules/formBuilder/FormBuilder.tsx`**: Dynamic form creation
- **`src/modules/formFill/FormFill.tsx`**: Form filling interface
- **`src/modules/forms/Forms.tsx`**: Form management
- **`src/modules/actions/Actions.tsx`**: Action management
- **`src/modules/membersAction/MembersAction.tsx`**: Member action management
- **`src/modules/invitation/Invitation.tsx`**: User invitation system
- **`src/modules/subscription/Subscription.tsx`**: Subscription management
- **`src/modules/subscribers/Subscribers.tsx`**: Subscriber management

### Specialized Modules
- **`src/modules/Refferer/Refferer.tsx`**: Referrer management
- **`src/modules/subContractor/Subcontractor.tsx`**: Subcontractor management
- **`src/modules/crew/`**: Crew management components
- **`src/modules/crewPositions/CrewPositions.tsx`**: Crew position management
- **`src/modules/profile/Profile.tsx`**: User profile management
- **`src/modules/company/Company.tsx`**: Company management
- **`src/modules/deleted/`**: Deleted items management across various entities

## Shared Components Library

### Core UI Components (`src/shared/components/`)
- **`actionButtons/ActionButtons.tsx`**: Reusable action buttons with icons
- **`alert/Alert.tsx`**: Alert and notification components
- **`button/Button.tsx`**: Generic button component with variants
- **`dropdown/Dropdown.tsx`**: Dropdown menu components
- **`grid/Grid.tsx`**: Grid layout components
- **`input/SearchBar.tsx`**: Search input components
- **`loader/Loader.tsx`**: Loading spinner components
- **`pill/Pill.tsx`**: Pill/badge components for status display
- **`popover/Popover.tsx`**: Popover and tooltip components
- **`profileInfo/ProfileInfo.tsx`**: Profile information display
- **`tabBar/TabBar.tsx`**: Tab navigation components
- **`tag/Tag.tsx`**: Tag components for categorization
- **`tooltip/Tooltip.tsx`**: Tooltip components with portal support

### Form Components (`src/shared/`)
- **`addressInput/AddressInput.tsx`**: Address input with Google Maps autocomplete
- **`autoComplete/AutoComplete.tsx`**: Generic autocomplete component
- **`autoCompleteAddress/AutoCompleteAddress.tsx`**: Address-specific autocomplete
- **`checkbox/Checkbox.tsx`**: Styled checkbox components
- **`customModal/CustomModal.tsx`**: Modal dialog components
- **`customSelect/CustomSelect.tsx`**: Custom select dropdown with styling
- **`date/SharedDate.tsx`**: Date picker components
- **`date/SharedDateAndTime.tsx`**: Date and time picker components
- **`dropDown/Dropdown.tsx`**: Various dropdown implementations
- **`formSelect/FormSelect.tsx`**: Form-integrated select components
- **`inputWithValidation/InputWithValidation.tsx`**: Input with validation display
- **`inputLabelWithValidation/InputLabelWithValidation.tsx`**: Labeled input with validation
- **`normalInput/NormalInput.tsx`**: Basic input components
- **`radioButtonGroup/RadioButtonGroup.tsx`**: Radio button group components
- **`searchableDropdown/SearchableDropdown.tsx`**: Searchable dropdown with filtering
- **`textArea/TextArea.tsx`**: Text area components with validation
- **`toggle/Toggle.tsx`**: Toggle switch components

### Specialized Shared Components
- **`advancedFilter/`**: Advanced filtering system with dynamic fields
- **`crewProjectDetails/CrewProjectDetails.tsx`**: Crew project detail views
- **`draggableDiv/DraggableDiv.tsx`**: Draggable container components
- **`scrollableDiv/ScrollableDiv.tsx`**: Scrollable container with custom styling
- **`shareLink/ShareLink.tsx`**: Link sharing components
- **`table/`**: Comprehensive table components with pagination, sorting, and filtering
- **`timecard/Timecard.tsx`**: Timecard-specific display components

### Utility Functions & Hooks (`src/shared/`)
- **`helpers/constants.ts`**: Application-wide constants
- **`helpers/util.ts`**: General utility functions
- **`helpers/regex.ts`**: Regular expression patterns
- **`helpers/images.ts`**: Image processing utilities
- **`helpers/map.ts`**: Map-related utilities
- **`helpers/projectCalculations.ts`**: Project calculation utilities
- **`helpers/yupExtension.ts`**: Yup validation extensions
- **`hooks/useClickOutside.ts`**: Hook for detecting clicks outside elements
- **`hooks/useDebounce.ts`**: Debouncing hook for performance optimization
- **`hooks/useImageUpload.ts`**: Image upload handling hook
- **`hooks/useWindowDimensions.ts`**: Window size tracking hook

## Assets & Resources

### Icons (`src/assets/icons/`)
- React components for various application icons
- SVG-based icons for scalability
- Organized by functionality (Add, Delete, Edit, etc.)

### New Icons (`src/assets/newIcons/`)
- Updated icon set in SVG format
- Comprehensive collection for all application features
- Consistent styling and sizing

### Images (`src/assets/images/`)
- Application logos and branding assets
- Background images and illustrations
- Static image resources

### Fonts (`src/assets/fonts/`)
- Custom font files and declarations
- Font type definitions for TypeScript

## Error Handling & Utilities

### Error Boundaries
- **`src/modules/app/errorBoundary/ErrorBoundary.tsx`**: React error boundary for graceful error handling

### Utility Components
- **`src/modules/app/scrollToTop/ScrollToTop.tsx`**: Scroll restoration for route changes
- **`src/modules/app/notFound/NotFound.tsx`**: 404 error page component

## Testing Setup
- **`src/setupTests.ts`**: Test environment configuration
- **`src/App.test.tsx`**: Basic application tests
- **`src/reportWebVitals.ts`**: Performance monitoring setup

## Type Definitions
- **`src/react-app-env.d.ts`**: React app environment types
- **`src/types.ts`**: Global type definitions and window extensions

## Configuration Files
- **`.eslintrc`**: ESLint configuration for code quality
- **`.prettierrc`**: Prettier configuration for code formatting
- **`commitlint.config.js`**: Commit message linting rules
