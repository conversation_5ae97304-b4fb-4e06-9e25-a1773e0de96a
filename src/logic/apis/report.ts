import { AxiosInstance } from '.'
import { endOfDate, notify, simplifyBackendError, startOfDate } from '../../shared/helpers/util'

interface IGetWeeklySalesReport {
  startDate: string
  endDate: string
}

interface IGetCustomReport {
  startDate: string
  endDate: string
  salesPersonId?: string
  csrId?: string
}
interface IRunPayRollReport {
  startDate: string
  endDate: string
  paySchId: string
}

export const getWeeklySalesReport = async (data: IGetWeeklySalesReport) => {
  try {
    const token: any = localStorage.getItem('token')
    // const startDate = startOfDate(new Date(data.startDate))
    // const endDate = endOfDate(new Date(data.endDate))
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(`/report/get-weekly-sales-report/${startDate}/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getWeeklySalesReport error', error)
    return error?.response
  }
}

export const getCustomReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(
      `/report/get-custom-sales-person-report/${startDate}/${endDate}?salesPersonId=${data?.salesPersonId}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getCustomReport error', error)
    return error?.response
  }
}
export const getCustomCSRReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(
      `/report/get-csr-report/${startDate}/${endDate}?salesPersonId=${data?.salesPersonId}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getCustomCSRReport error', error)
    return error?.response
  }
}

export const getSalesReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(
      `/report/get-sales-person-report/${startDate}/${endDate}?salesPersonId=${data?.salesPersonId}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getCustomReport error', error)
    return error?.response
  }
}

export const getConversionReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(
      `/report/conversion-report/${startDate}/${endDate}?salesPersonId=${data?.salesPersonId}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('get Conversion Report error', error)
    return error?.response
  }
}

export const getWeeklyProductionReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(new Date(data.startDate))
    const endDate = endOfDate(new Date(data.endDate))

    const response = await AxiosInstance.get(`/report/get-weekly-production-report/${startDate}/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getCustomReport error', error)
    return error?.response
  }
}

export const getWeeklyProjectReport = async ({
  startDate,
  endDate,
  crewIds,
}: {
  startDate: string
  endDate: string
  crewIds: string
}) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/report/get-weekly-project-report/${startOfDate(startDate)}/${endOfDate(endDate)}?crewIds=${crewIds}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getWeeklyProjectReport error', error)
    return error?.response
  }
}

export const getClientValueReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(`/report/client-value-report/${startDate}/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response?.data
  } catch (error: any) {
    console.error('getClientValueReport error', error)
    return error?.response
  }
}

export const getCrewReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(`/report/get-crew-scoreboard-report/${startDate}/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response?.data
  } catch (error: any) {
    console.error('getCustomReport error', error)
    return error?.response
  }
}

export const getKPIReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    // const startDate = data.startDate.includes('"') ? startOfDate(data.startDate) : startOfDate(`"${data.startDate}"`)
    // const endDate = data.endDate.includes('"') ? endOfDate(data.endDate) : endOfDate(`"${data.endDate}"`)

    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(`/report/get-kpi-report/${startDate}/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response?.data
  } catch (error: any) {
    console.error('getCustomReport error', error)
    return error?.response
  }
}

export const getProductionReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(`/report/get-production-report/${startDate}/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response?.data
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getCustomReport error', error)
    return error?.response
  }
}

export const getCommissionReport = async (data: IGetCustomReport) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const monthStart = new Date(startDate)
    monthStart.setDate(1)
    // /${monthStart.toISOString()}
    const response = await AxiosInstance.get(
      `/report/get-commission-report/${startDate}/${endDate}/${monthStart.toISOString()}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response?.data
  } catch (error: any) {
    console.error('getCustomReport error', error)
    return error?.response
  }
}

export const getPayRollReport = async (data: { startDate: string; endDate: string; salesPersonId?: string }) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(`/report/payroll-report/${startDate}/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        salesPersonId: data?.salesPersonId,
      },
    })
    return response?.data
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getPayRollReport error', error)
    return error?.response
  }
}

export const getCrewPayRollReport = async (data: { startDate: string; endDate: string }) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(`/report/crew-payroll-report/${startDate}/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response?.data
  } catch (error: any) {
    console.error('getCrewPayRollReport error', error)
    return error?.response
  }
}
export const getNonCrewPayRollReport = async (data: { startDate: string; endDate: string; salesPersonId?: string }) => {
  try {
    const token: any = localStorage.getItem('token')
    const startDate = startOfDate(data.startDate)
    const endDate = endOfDate(data.endDate)
    const response = await AxiosInstance.get(`/report/non-crew-payroll-report/${startDate}/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        salesPersonId: data?.salesPersonId,
      },
    })
    return response?.data
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getNonCrewPayRollReport error', error)
    return error?.response
  }
}
