<div align="center" style="margin-top:20px">

[![NHR](./src//assets/images/logo.png)]()

### New Heights Roofing

</div>
<hr>

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Technologies](#technologies)
- [Folder Structure](#folder-structure)
- [Module Index](#module-index)
- [API Structure](#api-structure)
- [Redux State Management](#redux-state-management)
- [Shared Components](#shared-components)
- [Routing](#routing)
- [Setup & Development](#setup--development)
- [Code Style](#code-style)

## Overview

New Heights Roofing is a comprehensive business management application built with React and TypeScript. It provides functionality for managing teams, projects, sales, operations, reporting, and various business settings for roofing companies.

## Architecture

The application follows a modular architecture with clear separation of concerns:

- **Frontend**: React 17 with TypeScript
- **State Management**: Redux with traditional store setup
- **Routing**: React Router v6
- **Styling**: Styled Components with theme support
- **Build Tool**: Webpack with custom configuration
- **Testing**: Jest with React Testing Library

## Technologies

### Core Technologies
- **React 17.0.2**: Frontend framework
- **TypeScript 4.5.4**: Type safety and development experience
- **Redux**: State management
- **React Router 6**: Client-side routing
- **Styled Components 5.3.0**: CSS-in-JS styling
- **Formik 2.2.9**: Form handling
- **Yup 0.32.11**: Form validation

### Additional Libraries
- **@reduxjs/toolkit**: Modern Redux development
- **axios**: HTTP client
- **dayjs**: Date manipulation
- **react-beautiful-dnd**: Drag and drop functionality
- **react-table**: Table components
- **apexcharts**: Data visualization
- **@react-google-maps/api**: Google Maps integration
- **@stripe/react-stripe-js**: Payment processing

## Folder Structure

```
nhrapp-react/
├── public/                     # Static assets
├── src/
│   ├── assets/                 # Images, fonts, icons
│   │   ├── fonts/             # Custom fonts
│   │   ├── icons/             # SVG icons and React icon components
│   │   ├── images/            # Static images
│   │   └── newIcons/          # Updated icon set
│   ├── logic/                 # Business logic and utilities
│   │   ├── apis/              # API service functions
│   │   ├── redux/             # Redux store, actions, reducers
│   │   └── paths.ts           # Route path definitions
│   ├── modules/               # Feature modules (pages/screens)
│   │   ├── app/               # App shell, routing, layout
│   │   ├── auth/              # Authentication flows
│   │   ├── dashboard/         # Dashboard and analytics
│   │   ├── team/              # Team management
│   │   ├── sales/             # Sales management
│   │   ├── operations/        # Operations management
│   │   ├── reports/           # Reporting modules
│   │   ├── settings/          # Application settings
│   │   └── [other modules]/   # Various business modules
│   ├── shared/                # Reusable components and utilities
│   │   ├── components/        # Generic UI components
│   │   ├── helpers/           # Utility functions
│   │   ├── hooks/             # Custom React hooks
│   │   └── [form components]/ # Form-related components
│   └── styles/                # Global styles and themes
├── webpack/                   # Webpack configuration
├── package.json
└── tsconfig.json
```

## Module Index

### Core Application Modules

#### Authentication (`src/modules/auth/`)
- **signin/**: User login functionality
- **signup/**: Company and user registration
- **pieceWorkProSignup/**: Specialized signup for piece work professionals
- **forgotPassword/**: Password recovery
- **resetPassword/**: Password reset functionality

#### Dashboard (`src/modules/dashboard/`)
- Main dashboard with KPIs and analytics
- Business overview and metrics

#### Team Management (`src/modules/team/`)
- **Team.tsx**: Main team overview
- **components/**: Team member management, compensation history
- Crew management and member profiles

#### Sales Management (`src/modules/sales/`)
- **Sales.tsx**: Main sales pipeline
- **AddOpportunityModal.tsx**: Create new opportunities
- **DeletedSales.tsx**: Manage deleted sales records
- **InactiveOpportunity.tsx**: Handle inactive opportunities
- **LostOpportunity.tsx**: Track lost opportunities
- **OldCompletedSales.tsx**: Historical sales data

#### Operations (`src/modules/operations/`)
- **Operations.tsx**: Operations management dashboard
- **component/**: Operational components
- **oldCompleted/**: Historical operations data

#### Leads Management (`src/modules/leads/`)
- **Leads.tsx**: Lead management interface
- **AddNewLead.tsx**: Create new leads
- **DeletedLeads.tsx**: Manage deleted leads
- **InactiveLeads.tsx**: Handle inactive leads
- **LostLeads.tsx**: Track lost leads

#### Project Management
- **newProject/**: Project creation and management
- **opportunity/**: Opportunity management
- **opportunityForOperations/**: Operations-specific opportunities
- **contract/**: Contract management
- **orderDetails/**: Order detail management

#### Reporting (`src/modules/reports/`)
- **Reports.tsx**: Main reporting dashboard
- **weeklyReport/**: Weekly business reports
- **salesPersonReport/**: Sales performance reports
- **weeklyProductionReport/**: Production metrics
- **crewReport/**: Crew performance reports
- **kpiReport/**: Key performance indicators
- **productionReport/**: Production analytics
- **commissionReport/**: Commission calculations
- **runPayroll/**: Payroll processing
- **clientValueReport/**: Client value analysis
- **marketingReport/**: Marketing effectiveness

#### Settings (`src/modules/settings/`)
- **Settings.tsx**: Main settings interface
- **adminSettings/**: Administrative settings
- **companySettings/**: Company configuration
- **departmentSettings/**: Department management
- **positionSettings/**: Position and role management
- **citySettings/**: City/location settings
- **taskSettings/**: Task configuration
- **crmSettings/**: CRM configuration
- **contractsSettings/**: Contract templates
- **mediaSettings/**: Media management settings

#### Business Configuration
- **client/**: Client management
- **contact/**: Contact management
- **leadSource/**: Lead source tracking
- **marketingChannel/**: Marketing channel management
- **materials/**: Materials catalog
- **units/**: Unit management
- **inputs/**: Input management
- **category/**: Category management
- **projectTypes/**: Project type definitions
- **taxJurisdiction/**: Tax jurisdiction settings
- **packages/**: Package management
- **tasks/**: Task management

#### Time & Payroll
- **clockInOut/**: Time tracking
- **timeCard/**: Timecard management
- **paySchedules/**: Pay schedule configuration
- **pieceWorkSettingsSalariedCrew/**: Piece work settings
- **pieceworkProjects/**: Piece work project management
- **salesCommission/**: Sales commission management

#### Utilities & Support
- **help/**: Help and support
- **media/**: Media management
- **track/**: GPS tracking
- **formBuilder/**: Dynamic form creation
- **formFill/**: Form filling interface
- **forms/**: Form management
- **actions/**: Action management
- **membersAction/**: Member action management
- **invitation/**: User invitation system
- **subscription/**: Subscription management
- **subscribers/**: Subscriber management

#### Specialized Modules
- **Refferer/**: Referrer management
- **subContractor/**: Subcontractor management
- **crew/**: Crew management
- **crewPositions/**: Crew position management
- **profile/**: User profile management
- **company/**: Company management
- **deleted/**: Deleted items management across various entities

## API Structure

The API layer is organized in `src/logic/apis/` with service functions for different domains:

### Core API Services
- **auth.ts**: Authentication and authorization
- **company.ts**: Company management
- **team.ts**: Team and member management
- **profile.ts**: User profile operations
- **invitation.ts**: User invitation system

### Business Domain APIs
- **client.ts**: Client management operations
- **contact.ts**: Contact management
- **sales.ts**: Sales pipeline operations
- **projects.ts**: Project management
- **crew.ts**: Crew management
- **subcontractor.ts**: Subcontractor operations

### Configuration APIs
- **city.ts**: City/location management
- **department.ts**: Department settings
- **position.ts**: Position and role management
- **leadSource.ts**: Lead source configuration
- **marketingChannel.ts**: Marketing channel management

### Operational APIs
- **clockInOut.ts**: Time tracking operations
- **approveTimeCard.ts**: Timecard approval
- **paySchedule.ts**: Pay schedule management
- **pieceWork.ts**: Piece work management
- **pieceWorkPro.ts**: Advanced piece work operations

### Reporting & Analytics
- **report.ts**: General reporting
- **dashboard.ts**: Dashboard data
- **compensation.ts**: Compensation calculations

### Utilities
- **form.ts**: Form management
- **media.ts**: Media upload and management
- **gps.ts**: GPS tracking
- **task.ts**: Task management
- **useFetch.ts**: Custom fetch hook
- **wheatherApi.ts**: Weather data integration

## Redux State Management

The Redux store is configured in `src/logic/redux/` with the following structure:

### Store Configuration (`store.tsx`)
- Traditional Redux store setup with Redux DevTools integration
- Environment-based configuration

### State Slices
- **auth**: Authentication state, user session
- **company**: Current company information
- **invitation**: Invitation management state
- **timeZone**: Timezone configuration
- **ui**: UI state management (modals, loading states, etc.)

### Actions & Reducers
Each domain has corresponding actions and reducers:
- `src/logic/redux/actions/`: Action creators
- `src/logic/redux/reducers/`: State reducers
- `src/logic/redux/reduxHook.ts`: Typed Redux hooks

## Shared Components

The `src/shared/` directory contains reusable components and utilities:

### UI Components (`src/shared/components/`)
- **actionButtons/**: Action button components with icons
- **alert/**: Alert and notification components
- **button/**: Generic button component
- **dropdown/**: Dropdown menu components
- **grid/**: Grid layout components
- **input/**: Input components including search bars
- **loader/**: Loading spinner components
- **pill/**: Pill/badge components
- **popover/**: Popover and tooltip components
- **profileInfo/**: Profile information display
- **tabBar/**: Tab navigation components
- **tag/**: Tag components
- **tooltip/**: Tooltip components

### Form Components
- **addressInput/**: Address input with autocomplete
- **autoComplete/**: Generic autocomplete component
- **autoCompleteAddress/**: Address-specific autocomplete
- **checkbox/**: Checkbox components
- **customModal/**: Modal dialog components
- **customSelect/**: Custom select dropdown
- **date/**: Date and datetime pickers
- **dropDown/**: Various dropdown implementations
- **formSelect/**: Form-integrated select components
- **inputWithValidation/**: Input with validation
- **normalInput/**: Basic input components
- **radioButtonGroup/**: Radio button groups
- **searchableDropdown/**: Searchable dropdown
- **textArea/**: Text area components
- **toggle/**: Toggle switch components

### Specialized Components
- **advancedFilter/**: Advanced filtering system
- **crewProjectDetails/**: Crew project detail views
- **draggableDiv/**: Draggable container components
- **scrollableDiv/**: Scrollable container
- **shareLink/**: Link sharing components
- **table/**: Table components with pagination and sorting
- **timecard/**: Timecard-specific components

### Utilities
- **helpers/**: Utility functions and constants
- **hooks/**: Custom React hooks (useClickOutside, useDebounce, etc.)

## Routing

The application uses React Router v6 with a centralized routing configuration:

### Route Configuration (`src/modules/app/routes/NewRoute.tsx`)
- **AuthLayout**: Routes for unauthenticated users (login, signup, etc.)
- **DashboardLayout**: Protected routes for authenticated users
- **Route Protection**: Based on authentication status and subscription plan

### Key Route Categories
- **Authentication Routes**: `/`, `/signin`, `/signup`, `/forgot-password`
- **Dashboard**: `/dashboard`
- **Team Management**: `/team`, `/team/crew`, `/team/member/:id`
- **Sales**: `/sales`, `/leads`, `/sales/opportunity/:id`
- **Operations**: `/operations`, `/operations/opportunity/:id`
- **Reports**: `/reports/*` (various report types)
- **Settings**: `/settings/*` (various configuration pages)
- **Time Tracking**: `/clock-in-out`, `/time-cards/*`

### Path Definitions (`src/logic/paths.ts`)
- Centralized path constants with TypeScript interfaces
- Dynamic route parameters with type safety
- URL generation helpers

## Setup & Development

### First Time Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/tysonfaulkner/nhrapp-react.git
   cd nhrapp-react
   ```

2. **Install dependencies**:
   ```bash
   yarn install
   ```

3. **Environment Configuration**:
   - Set up environment variables as needed
   - Configure API endpoints

### Development Commands

```bash
# Start development server
yarn start
yarn dev

# Build for production
yarn build

# Run tests
yarn test

# Lint code
yarn lint

# Format code
yarn pre-commit
```

### Build Configuration
- **Webpack**: Custom webpack configuration in `/webpack/`
- **Development**: Hot reloading with webpack-dev-server
- **Production**: Optimized builds with code splitting
- **TypeScript**: Full TypeScript support with strict mode

## Code Style

### Naming Conventions
- **Folders**: camelCase
- **Files**: PascalCase for components, camelCase for utilities
- **Functions**: camelCase
- **Components**: PascalCase
- **Constants**: UPPER_SNAKE_CASE

### Code Quality
- **Prettier**: Automatic code formatting
- **ESLint**: Code linting with TypeScript rules
- **Husky**: Pre-commit hooks for code quality
- **Lint-staged**: Run linters on staged files only

### Development Rules
- No `console.log` statements in production code
- Proper TypeScript typing for all functions and components
- Use styled-components for styling
- Follow React best practices and hooks patterns
- Implement proper error boundaries
- Use proper loading states and error handling

### File Organization
- Each module should have its own `style.ts` file for styled-components
- Components should be in their own directories with index files
- Shared utilities should be properly categorized
- API functions should be grouped by domain